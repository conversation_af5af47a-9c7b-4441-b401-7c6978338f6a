import {
    Content,
    IAgentRuntime,
    IImageDescriptionService,
    Memory,
    State,
    UUID,
    getEmbeddingZeroVector,
    elizaLogger,
    stringToUuid,
} from "@elizaos/core";
import {
    QueryTweetsResponse,
    QueryProfilesResponse,
    Scraper,
    SearchMode,
    Tweet,
} from "agent-twitter-client";
import {
    <PERSON><PERSON><PERSON>,
    TweetV2SingleResult,
    TwitterV2Includes<PERSON>elper,
    TweetV2,
    UserV2,
} from "twitter-api-v2";
import { EventEmitter } from "events";
import { TwitterConfig } from "./environment.ts";
import { TwitterOAuthService } from "./oauth.ts";
export function extractAnswer(text: string): string {
    const startIndex = text.indexOf("Answer: ") + 8;
    const endIndex = text.indexOf("<|endoftext|>", 11);
    return text.slice(startIndex, endIndex);
}

type TwitterProfile = {
    id: string;
    username: string;
    screenName: string;
    bio: string;
    nicknames: string[];
};

class RequestQueue {
    private queue: (() => Promise<any>)[] = [];
    private processing: boolean = false;

    async add<T>(request: () => Promise<T>): Promise<T> {
        return new Promise((resolve, reject) => {
            this.queue.push(async () => {
                try {
                    const result = await request();
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            });
            this.processQueue();
        });
    }

    private async processQueue(): Promise<void> {
        if (this.processing || this.queue.length === 0) {
            return;
        }
        this.processing = true;

        while (this.queue.length > 0) {
            const request = this.queue.shift()!;
            try {
                await request();
            } catch (error) {
                console.error("Error processing request:", error);
                this.queue.unshift(request);
                await this.exponentialBackoff(this.queue.length);
            }
            await this.randomDelay();
        }

        this.processing = false;
    }

    private async exponentialBackoff(retryCount: number): Promise<void> {
        const delay = Math.pow(2, retryCount) * 1000;
        await new Promise((resolve) => setTimeout(resolve, delay));
    }

    private async randomDelay(): Promise<void> {
        const delay = Math.floor(Math.random() * 2000) + 1500;
        await new Promise((resolve) => setTimeout(resolve, delay));
    }
}

export class ClientBase extends EventEmitter {
    static _twitterClients: { [accountIdentifier: string]: TwitterApi } = {};
    twitterClient: TwitterApi;
    runtime: IAgentRuntime;
    twitterConfig: TwitterConfig;
    directions: string;
    lastCheckedTweetId: bigint | null = null;
    imageDescriptionService: IImageDescriptionService;
    temperature: number = 0.5;
    oauthService: TwitterOAuthService;

    requestQueue: RequestQueue = new RequestQueue();

    profile: TwitterProfile | null;

    async cacheTweet(tweet: Tweet): Promise<void> {
        if (!tweet) {
            console.warn("Tweet is undefined, skipping cache");
            return;
        }

        this.runtime.cacheManager.set(`twitter/tweets/${tweet.id}`, tweet);
    }

    async getCachedTweet(tweetId: string): Promise<Tweet | undefined> {
        const cached = await this.runtime.cacheManager.get<Tweet>(
            `twitter/tweets/${tweetId}`
        );

        return cached;
    }

    async getTweet(tweetId: string): Promise<Tweet> {
        const cachedTweet = await this.getCachedTweet(tweetId);

        if (cachedTweet) {
            return cachedTweet;
        }

        const tweetResult = await this.requestQueue.add(() =>
            this.twitterClient.v2.singleTweet(tweetId, {
                "tweet.fields": [
                    "created_at",
                    "author_id",
                    "conversation_id",
                    "public_metrics",
                    "text",
                    "context_annotations",
                    "in_reply_to_user_id",
                ],
                "user.fields": ["username", "name", "description"],
                expansions: ["author_id"],
            })
        );

        // Convert TweetV2SingleResult to Tweet format
        const tweet: Tweet = {
            id: tweetResult.data.id,
            text: tweetResult.data.text || "",
            conversationId: tweetResult.data.conversation_id,
            timestamp: tweetResult.data.created_at
                ? new Date(tweetResult.data.created_at).getTime() / 1000
                : Date.now() / 1000,
            userId: tweetResult.data.author_id || "",
            inReplyToStatusId: tweetResult.data.in_reply_to_user_id,
            permanentUrl: tweetResult.includes?.users?.[0]?.username
                ? `https://x.com/${tweetResult.includes.users[0].username}/status/${tweetResult.data.id}`
                : `https://x.com/i/status/${tweetResult.data.id}`,
            hashtags: [],
            mentions: [],
            photos: [],
            thread: [],
            urls: [],
            videos: [],
            username: tweetResult.includes?.users?.[0]?.username || "",
            name: tweetResult.includes?.users?.[0]?.name || "",
        };

        await this.cacheTweet(tweet);
        return tweet;
    }

    callback: (self: ClientBase) => any = null;

    onReady() {
        throw new Error(
            "Not implemented in base class, please call from subclass"
        );
    }

    constructor(runtime: IAgentRuntime, twitterConfig: TwitterConfig) {
        super();
        this.runtime = runtime;
        this.twitterConfig = twitterConfig;
        this.oauthService = new TwitterOAuthService(runtime);
        const username = this.runtime.getSetting("TWITTER_USERNAME");
        if (ClientBase._twitterClients[username]) {
            this.twitterClient = ClientBase._twitterClients[username];
        } else {
            this.twitterClient = this.oauthService.getScraper();
            ClientBase._twitterClients[username] = this.twitterClient;
        }

        this.directions =
            "- " +
            this.runtime.character.style.all.join("\n- ") +
            "- " +
            this.runtime.character.style.post.join();
    }

    async init(email, username, password) {
        const twitter2faSecret = this.twitterConfig.TWITTER_2FA_SECRET;
        const appKey = this.twitterConfig.TWITTER_APP_KEY;
        const appSecret = this.twitterConfig.TWITTER_APP_SECRET;
        const accessToken = this.twitterConfig.TWITTER_ACCESS_TOKEN;
        const accessSecret = this.twitterConfig.TWITTER_ACCESS_SECRET;

        if (!username) {
            throw new Error("Twitter username not configured");
        }

        // Try OAuth authentication first if credentials are available
        if (appKey && appSecret) {
            elizaLogger.info("Attempting OAuth authentication");

            // First try with stored tokens
            if (accessToken && accessSecret) {
                elizaLogger.info("Using provided OAuth tokens");
                try {
                    await this.twitterClient.login(
                        username,
                        password,
                        email,
                        twitter2faSecret,
                        appKey,
                        appSecret,
                        accessToken,
                        accessSecret
                    );

                    if (await this.twitterClient.isLoggedIn()) {
                        elizaLogger.info(
                            "Successfully logged in with OAuth tokens"
                        );

                        try {
                            this.profile = await this.fetchProfileWithRetry(
                                username,
                                3
                            );
                            if (this.profile) {
                                await this.setupProfile();
                                await this.loadLatestCheckedTweetId();

                                // Try to populate timeline but don't fail if it times out
                                // try {
                                //     elizaLogger.log(
                                //         "Starting timeline population..."
                                //     );
                                //     await Promise.race([
                                //         this.populateTimeline(),
                                //         new Promise((_, reject) =>
                                //             setTimeout(
                                //                 () =>
                                //                     reject(
                                //                         new Error(
                                //                             "Timeline population timeout"
                                //                         )
                                //                     ),
                                //                 15000
                                //             )
                                //         ),
                                //     ]);
                                //     elizaLogger.log(
                                //         "Timeline population completed successfully"
                                //     );
                                // } catch (timelineError) {
                                //     elizaLogger.warn(
                                //         "Timeline population failed, continuing without timeline:",
                                //         timelineError
                                //     );
                                // }

                                return true;
                            } else {
                                elizaLogger.error(
                                    "Failed to fetch profile after retries"
                                );
                            }
                        } catch (profileError) {
                            elizaLogger.error(
                                "Error during profile initialization:",
                                profileError
                            );
                        }
                    }
                } catch (error) {
                    elizaLogger.error(
                        "Error logging in with OAuth tokens:",
                        error
                    );
                }
            } else {
                // Try with cached tokens
                const authenticated =
                    await this.oauthService.authenticateWithStoredTokens(
                        appKey,
                        appSecret
                    );

                if (authenticated) {
                    // Make sure we're using the authenticated scraper
                    this.twitterClient = this.oauthService.getScraper();
                    ClientBase._twitterClients[username] = this.twitterClient;

                    try {
                        // Initialize Twitter profile with retry logic
                        this.profile = await this.fetchProfileWithRetry(
                            username,
                            3
                        );
                        if (this.profile) {
                            await this.setupProfile();
                            await this.loadLatestCheckedTweetId();

                            // Try to populate timeline but don't fail if it times out
                            // try {
                            //     await Promise.race([
                            //         this.populateTimeline(),
                            //         new Promise((_, reject) =>
                            //             setTimeout(
                            //                 () =>
                            //                     reject(
                            //                         new Error(
                            //                             "Timeline population timeout"
                            //                         )
                            //                     ),
                            //                 15000
                            //             )
                            //         ),
                            //     ]);
                            // } catch (timelineError) {
                            //     elizaLogger.warn(
                            //         "Timeline population failed, continuing without timeline:",
                            //         timelineError
                            //     );
                            // }
                            return true;
                        } else {
                            elizaLogger.error(
                                "Failed to fetch profile after retries with cached tokens"
                            );
                        }
                    } catch (profileError) {
                        elizaLogger.error(
                            "Error during profile initialization with cached tokens:",
                            profileError
                        );
                    }
                }
            }
        }

        const cachedCookies = await this.getCachedCookies(username);

        if (cachedCookies) {
            elizaLogger.info("Using cached cookies");
            await this.setCookiesFromArray(cachedCookies);
        }

        // Initialize Twitter profile with retry logic
        try {
            this.profile = await this.fetchProfileWithRetry(username, 3);
            if (this.profile) {
                await this.setupProfile();
                await this.loadLatestCheckedTweetId();

                // Try to populate timeline but don't fail if it times out
                // try {
                //     await Promise.race([
                //         this.populateTimeline(),
                //         new Promise((_, reject) =>
                //             setTimeout(
                //                 () =>
                //                     reject(
                //                         new Error("Timeline population timeout")
                //                     ),
                //                 15000
                //             )
                //         ),
                //     ]);
                // } catch (timelineError) {
                //     elizaLogger.warn(
                //         "Timeline population failed, continuing without timeline:",
                //         timelineError
                //     );
                // }

                return true;
            } else {
                elizaLogger.error(
                    "Failed to fetch profile after retries with fallback authentication"
                );
                return false;
            }
        } catch (profileError) {
            elizaLogger.error(
                "Error during profile initialization with fallback authentication:",
                profileError
            );
            return false;
        }
    }

    private async setupProfile() {
        if (this.profile) {
            elizaLogger.log("Twitter user ID:", this.profile.id);
            elizaLogger.log(
                "Twitter loaded:",
                JSON.stringify(this.profile, null, 10)
            );
            // Store profile info for use in responses
            this.runtime.character.twitterProfile = {
                id: this.profile.id,
                username: this.profile.username,
                screenName: this.profile.screenName,
                bio: this.profile.bio,
                nicknames: this.profile.nicknames,
            };
        } else {
            throw new Error("Failed to load profile");
        }
    }

    async fetchOwnPosts(count: number): Promise<Tweet[]> {
        elizaLogger.debug("fetching own posts");
        const homeTimeline = await this.twitterClient.getUserTweets(
            this.profile.id,
            count
        );
        return homeTimeline.tweets;
    }

    /**
     * Fetch timeline for twitter account, optionally only from followed accounts
     */
    async fetchHomeTimeline(
        count: number,
        following?: boolean
    ): Promise<Tweet[]> {
        elizaLogger.log(
            "fetching home timeline, count:",
            count,
            "following:",
            following
        );
        elizaLogger.log(
            "Twitter client auth status:",
            await this.twitterClient.isLoggedIn()
        );

        try {
            // Check if we have OAuth authentication and can use v2 API
            const v2Client = this.twitterClient.getOAuthV2Client();
            if (v2Client) {
                elizaLogger.log("Using Twitter API v2 for timeline fetching");

                try {
                    // Use Twitter API v2 to fetch home timeline
                    const v2Timeline = await v2Client.v2.homeTimeline({
                        max_results: Math.min(count, 100), // v2 API limit is 100
                        "tweet.fields": [
                            "created_at",
                            "author_id",
                            "conversation_id",
                            "public_metrics",
                            "text",
                            "context_annotations",
                        ],
                        "user.fields": ["username", "name", "description"],
                        expansions: ["author_id"],
                    });

                    elizaLogger.log(
                        "V2 API response received, tweets count:",
                        v2Timeline.data?.data?.length || 0
                    );

                    if (
                        v2Timeline.data?.data &&
                        v2Timeline.data.data.length > 0
                    ) {
                        // Convert v2 tweets to the expected format
                        const convertedTweets = v2Timeline.data.data.map(
                            (tweet) => {
                                const author =
                                    v2Timeline.data?.includes?.users?.find(
                                        (user) => user.id === tweet.author_id
                                    );
                                return {
                                    id: tweet.id,
                                    text: tweet.text,
                                    name: author?.name || "",
                                    username: author?.username || "",
                                    userId: tweet.author_id,
                                    conversationId: tweet.conversation_id,
                                    timestamp: tweet.created_at
                                        ? new Date(tweet.created_at).getTime() /
                                          1000
                                        : Date.now() / 1000,
                                    createdAt: tweet.created_at,
                                    permanentUrl: `https://x.com/${author?.username}/status/${tweet.id}`,
                                    hashtags: [],
                                    mentions: [],
                                    photos: [],
                                    videos: [],
                                    urls: [],
                                    thread: [],
                                    inReplyToStatusId: null,
                                    likes:
                                        tweet.public_metrics?.like_count || 0,
                                    retweets:
                                        tweet.public_metrics?.retweet_count ||
                                        0,
                                    replies:
                                        tweet.public_metrics?.reply_count || 0,
                                };
                            }
                        );

                        elizaLogger.log(
                            "Successfully converted v2 tweets to expected format"
                        );
                        return convertedTweets;
                    } else {
                        elizaLogger.warn("V2 API returned no tweets");
                        return [];
                    }
                } catch (v2Error) {
                    elizaLogger.warn(
                        "V2 API failed, falling back to scraper:",
                        v2Error
                    );
                    // Fall through to scraper method
                }
            }

            elizaLogger.log(
                "About to call scraper method:",
                following ? "fetchFollowingTimeline" : "fetchHomeTimeline"
            );
            const homeTimeline = following
                ? await this.twitterClient.fetchFollowingTimeline(count, [])
                : await this.twitterClient.fetchHomeTimeline(count, []);

            elizaLogger.log(
                "Successfully fetched timeline, tweets count:",
                homeTimeline?.length || 0
            );
            if (homeTimeline?.length > 0) {
                elizaLogger.log(
                    "First tweet sample:",
                    JSON.stringify(homeTimeline[0], null, 2)
                );
            } else {
                elizaLogger.warn("Timeline is empty or undefined");
            }

            const processedTimeline = homeTimeline
                .filter((t) => t.__typename !== "TweetWithVisibilityResults") // what's this about?
                .map((tweet) => {
                    //console.log("tweet is", tweet);
                    const obj = {
                        id: tweet.id,
                        name:
                            tweet.name ??
                            tweet?.user_results?.result?.legacy.name,
                        username:
                            tweet.username ??
                            tweet.core?.user_results?.result?.legacy
                                .screen_name,
                        text: tweet.text ?? tweet.legacy?.full_text,
                        inReplyToStatusId:
                            tweet.inReplyToStatusId ??
                            tweet.legacy?.in_reply_to_status_id_str ??
                            null,
                        timestamp:
                            new Date(tweet.legacy?.created_at).getTime() / 1000,
                        createdAt:
                            tweet.createdAt ??
                            tweet.legacy?.created_at ??
                            tweet.core?.user_results?.result?.legacy.created_at,
                        userId: tweet.userId ?? tweet.legacy?.user_id_str,
                        conversationId:
                            tweet.conversationId ??
                            tweet.legacy?.conversation_id_str,
                        permanentUrl: `https://x.com/${tweet.core?.user_results?.result?.legacy?.screen_name}/status/${tweet.rest_id}`,
                        hashtags:
                            tweet.hashtags ?? tweet.legacy?.entities.hashtags,
                        mentions:
                            tweet.mentions ??
                            tweet.legacy?.entities.user_mentions,
                        photos:
                            tweet.photos ??
                            tweet.legacy?.entities.media?.filter(
                                (media) => media.type === "photo"
                            ) ??
                            [],
                        thread: tweet.thread || [],
                        urls: tweet.urls ?? tweet.legacy?.entities.urls,
                        videos:
                            tweet.videos ??
                            tweet.legacy?.entities.media?.filter(
                                (media) => media.type === "video"
                            ) ??
                            [],
                    };
                    //console.log("obj is", obj);
                    return obj;
                });
            //elizaLogger.debug("process homeTimeline", processedTimeline);
            return processedTimeline;
        } catch (error) {
            elizaLogger.error("Error fetching home timeline:", error);
            return [];
        }
    }

    async fetchTimelineForActions(count: number): Promise<Tweet[]> {
        elizaLogger.debug("fetching timeline for actions");

        const agentUsername = this.runtime.getSetting("TWITTER_USERNAME");
        const homeTimeline = await this.twitterClient.fetchFollowingTimeline(
            count,
            []
        );

        return homeTimeline
            .map((tweet) => ({
                id: tweet.rest_id,
                name: tweet.core?.user_results?.result?.legacy?.name,
                username: tweet.core?.user_results?.result?.legacy?.screen_name,
                text: tweet.legacy?.full_text,
                inReplyToStatusId: tweet.legacy?.in_reply_to_status_id_str,
                timestamp: new Date(tweet.legacy?.created_at).getTime() / 1000,
                userId: tweet.legacy?.user_id_str,
                conversationId: tweet.legacy?.conversation_id_str,
                permanentUrl: `https://twitter.com/${tweet.core?.user_results?.result?.legacy?.screen_name}/status/${tweet.rest_id}`,
                hashtags: tweet.legacy?.entities?.hashtags || [],
                mentions: tweet.legacy?.entities?.user_mentions || [],
                photos:
                    tweet.legacy?.entities?.media?.filter(
                        (media) => media.type === "photo"
                    ) || [],
                thread: tweet.thread || [],
                urls: tweet.legacy?.entities?.urls || [],
                videos:
                    tweet.legacy?.entities?.media?.filter(
                        (media) => media.type === "video"
                    ) || [],
            }))
            .filter((tweet) => tweet.username !== agentUsername); // do not perform action on self-tweets
    }

    async fetchSearchTweets(
        query: string,
        maxTweets: number,
        searchMode: (typeof SearchMode)[keyof typeof SearchMode],
        cursor?: string
    ): Promise<QueryTweetsResponse> {
        try {
            // Sometimes this fails because we are rate limited. in this case, we just need to return an empty array
            // if we dont get a response in 5 seconds, something is wrong
            const timeoutPromise = new Promise((resolve) =>
                setTimeout(() => resolve({ tweets: [] }), 10000)
            );

            try {
                const result = await this.requestQueue.add(
                    async () =>
                        await Promise.race([
                            this.twitterClient.fetchSearchTweets(
                                query,
                                maxTweets,
                                searchMode,
                                cursor
                            ),
                            timeoutPromise,
                        ])
                );
                return (result ?? { tweets: [] }) as QueryTweetsResponse;
            } catch (error) {
                elizaLogger.error("Error fetching search tweets:", error);
                return { tweets: [] };
            }
        } catch (error) {
            elizaLogger.error("Error fetching search tweets:", error);
            return { tweets: [] };
        }
    }

    async fetchSearchProfiles(
        query: string,
        maxProfiles: number,
        cursor?: string
    ): Promise<QueryProfilesResponse> {
        try {
            // Sometimes this fails because we are rate limited. in this case, we just need to return an empty array
            // if we dont get a response in 5 seconds, something is wrong
            const timeoutPromise = new Promise((resolve) =>
                setTimeout(() => resolve({ profiles: [] }), 10000)
            );
            let followersCount = 0;
            try {
                const userProfile = await this.twitterClient.getProfile(
                    this.profile.username
                );
                followersCount = userProfile.followersCount;
            } catch (error) {
                elizaLogger.error("Error fetching profile:", error);
            }
            if (followersCount < 1000) {
                try {
                    const result = await Promise.race([
                        this.twitterClient.fetchSearchProfiles(
                            query,
                            maxProfiles,
                            cursor
                        ),
                        timeoutPromise,
                    ]);
                    return (result ?? {
                        profiles: [],
                    }) as QueryProfilesResponse;
                } catch (error) {
                    elizaLogger.error("Error fetching search profiles:", error);
                    return { profiles: [] };
                }
            }
        } catch (error) {
            elizaLogger.error("Error fetching search profiles:", error);
            return { profiles: [] };
        }
    }

    private async populateTimeline() {
        elizaLogger.log("populating timeline...");

        const cachedTimeline = await this.getCachedTimeline();
        elizaLogger.log(
            "Cached timeline exists:",
            !!cachedTimeline,
            "length:",
            cachedTimeline?.length || 0
        );

        // Check if the cache file exists
        if (cachedTimeline) {
            // Read the cached search results from the file

            // Get the existing memories from the database
            const existingMemories =
                await this.runtime.messageManager.getMemoriesByRoomIds({
                    roomIds: cachedTimeline.map((tweet) =>
                        stringToUuid(
                            tweet.conversationId + "-" + this.runtime.agentId
                        )
                    ),
                });

            //TODO: load tweets not in cache?

            // Create a Set to store the IDs of existing memories
            const existingMemoryIds = new Set(
                existingMemories.map((memory) => memory.id.toString())
            );

            // Check if any of the cached tweets exist in the existing memories
            const someCachedTweetsExist = cachedTimeline.some((tweet) =>
                existingMemoryIds.has(
                    stringToUuid(tweet.id + "-" + this.runtime.agentId)
                )
            );

            if (someCachedTweetsExist) {
                // Filter out the cached tweets that already exist in the database
                const tweetsToSave = cachedTimeline.filter(
                    (tweet) =>
                        !existingMemoryIds.has(
                            stringToUuid(tweet.id + "-" + this.runtime.agentId)
                        )
                );

                console.log({
                    processingTweets: tweetsToSave
                        .map((tweet) => tweet.id)
                        .join(","),
                });

                // Save the missing tweets as memories
                for (const tweet of tweetsToSave) {
                    elizaLogger.log("Saving Tweet", tweet.id);

                    const roomId = stringToUuid(
                        tweet.conversationId + "-" + this.runtime.agentId
                    );

                    const userId =
                        tweet.userId === this.profile.id
                            ? this.runtime.agentId
                            : stringToUuid(tweet.userId);

                    if (tweet.userId === this.profile.id) {
                        await this.runtime.ensureConnection(
                            this.runtime.agentId,
                            roomId,
                            this.profile.username,
                            this.profile.screenName,
                            "twitter"
                        );
                    } else {
                        await this.runtime.ensureConnection(
                            userId,
                            roomId,
                            tweet.username,
                            tweet.name,
                            "twitter"
                        );
                    }

                    const content = {
                        text: tweet.text,
                        url: tweet.permanentUrl,
                        source: "twitter",
                        inReplyTo: tweet.inReplyToStatusId
                            ? stringToUuid(
                                  tweet.inReplyToStatusId +
                                      "-" +
                                      this.runtime.agentId
                              )
                            : undefined,
                    } as Content;

                    elizaLogger.log("Creating memory for tweet", tweet.id);

                    // check if it already exists
                    const memory =
                        await this.runtime.messageManager.getMemoryById(
                            stringToUuid(tweet.id + "-" + this.runtime.agentId)
                        );

                    if (memory) {
                        elizaLogger.log(
                            "Memory already exists, skipping timeline population"
                        );
                        break;
                    }

                    await this.runtime.messageManager.createMemory({
                        id: stringToUuid(tweet.id + "-" + this.runtime.agentId),
                        userId,
                        content: content,
                        agentId: this.runtime.agentId,
                        roomId,
                        embedding: getEmbeddingZeroVector(),
                        createdAt: tweet.timestamp * 1000,
                    });

                    await this.cacheTweet(tweet);
                }

                elizaLogger.log(
                    `Populated ${tweetsToSave.length} missing tweets from the cache.`
                );
                return;
            }
        }
        elizaLogger.log(
            "About to fetch home timeline, count:",
            cachedTimeline ? 10 : 50
        );
        const timeline = await this.fetchHomeTimeline(cachedTimeline ? 10 : 50);
        elizaLogger.log("Fetched timeline, length:", timeline?.length || 0);
        const username = this.runtime.getSetting("TWITTER_USERNAME");

        // Get the most recent 20 mentions and interactions
        const mentionsAndInteractions = await this.fetchSearchTweets(
            `@${username}`,
            20,
            SearchMode.Latest
        );

        // Combine the timeline tweets and mentions/interactions
        const allTweets = [...timeline, ...mentionsAndInteractions.tweets];

        // Create a Set to store unique tweet IDs
        const tweetIdsToCheck = new Set<string>();
        const roomIds = new Set<UUID>();

        // Add tweet IDs to the Set
        for (const tweet of allTweets) {
            tweetIdsToCheck.add(tweet.id);
            roomIds.add(
                stringToUuid(tweet.conversationId + "-" + this.runtime.agentId)
            );
        }

        // Check the existing memories in the database
        const existingMemories =
            await this.runtime.messageManager.getMemoriesByRoomIds({
                roomIds: Array.from(roomIds),
            });

        // Create a Set to store the existing memory IDs
        const existingMemoryIds = new Set<UUID>(
            existingMemories.map((memory) => memory.id)
        );

        // Filter out the tweets that already exist in the database
        const tweetsToSave = allTweets.filter(
            (tweet) =>
                !existingMemoryIds.has(
                    stringToUuid(tweet.id + "-" + this.runtime.agentId)
                )
        );

        elizaLogger.debug({
            processingTweets: tweetsToSave.map((tweet) => tweet.id).join(","),
        });

        await this.runtime.ensureUserExists(
            this.runtime.agentId,
            this.profile.username,
            this.runtime.character.name,
            "twitter"
        );

        // Save the new tweets as memories
        for (const tweet of tweetsToSave) {
            elizaLogger.log("Saving Tweet", tweet.id);

            const roomId = stringToUuid(
                tweet.conversationId + "-" + this.runtime.agentId
            );
            const userId =
                tweet.userId === this.profile.id
                    ? this.runtime.agentId
                    : stringToUuid(tweet.userId);

            if (tweet.userId === this.profile.id) {
                await this.runtime.ensureConnection(
                    this.runtime.agentId,
                    roomId,
                    this.profile.username,
                    this.profile.screenName,
                    "twitter"
                );
            } else {
                await this.runtime.ensureConnection(
                    userId,
                    roomId,
                    tweet.username,
                    tweet.name,
                    "twitter"
                );
            }

            const content = {
                text: tweet.text,
                url: tweet.permanentUrl,
                source: "twitter",
                inReplyTo: tweet.inReplyToStatusId
                    ? stringToUuid(tweet.inReplyToStatusId)
                    : undefined,
            } as Content;

            await this.runtime.messageManager.createMemory({
                id: stringToUuid(tweet.id + "-" + this.runtime.agentId),
                userId,
                content: content,
                agentId: this.runtime.agentId,
                roomId,
                embedding: getEmbeddingZeroVector(),
                createdAt: tweet.timestamp * 1000,
            });

            await this.cacheTweet(tweet);
        }

        // Cache
        await this.cacheTimeline(timeline);
        await this.cacheMentions(mentionsAndInteractions.tweets);

        const allOwnPosts = allTweets.filter(
            (tweet) => tweet.userId === this.profile.id
        );
        if (allOwnPosts.length > 0) {
            await this.cacheOwnPosts(allOwnPosts);
        }
    }

    async setCookiesFromArray(cookiesArray: any[]) {
        const cookieStrings = cookiesArray.map(
            (cookie) =>
                `${cookie.key}=${cookie.value}; Domain=${cookie.domain}; Path=${cookie.path}; ${
                    cookie.secure ? "Secure" : ""
                }; ${cookie.httpOnly ? "HttpOnly" : ""}; SameSite=${
                    cookie.sameSite || "Lax"
                }`
        );
        await this.twitterClient.setCookies(cookieStrings);
    }

    async saveRequestMessage(message: Memory, state: State) {
        if (message.content.text) {
            const recentMessage = await this.runtime.messageManager.getMemories(
                {
                    roomId: message.roomId,
                    count: 1,
                    unique: false,
                }
            );

            if (
                recentMessage.length > 0 &&
                recentMessage[0].content === message.content
            ) {
                elizaLogger.debug("Message already saved", recentMessage[0].id);
            } else {
                await this.runtime.messageManager.createMemory({
                    ...message,
                    embedding: getEmbeddingZeroVector(),
                });
            }

            await this.runtime.evaluate(message, {
                ...state,
                twitterClient: this.twitterClient,
            });
        }
    }

    async loadLatestCheckedTweetId(): Promise<void> {
        const latestCheckedTweetId =
            await this.runtime.cacheManager.get<string>(
                `twitter/${this.profile.username}/latest_checked_tweet_id`
            );

        if (latestCheckedTweetId) {
            this.lastCheckedTweetId = BigInt(latestCheckedTweetId);
        }
    }

    async cacheLatestCheckedTweetId() {
        if (this.lastCheckedTweetId) {
            await this.runtime.cacheManager.set(
                `twitter/${this.profile.username}/latest_checked_tweet_id`,
                this.lastCheckedTweetId.toString()
            );
        }
    }

    async getCachedTimeline(): Promise<Tweet[] | undefined> {
        return await this.runtime.cacheManager.get<Tweet[]>(
            `twitter/${this.profile.username}/timeline`
        );
    }

    async cacheTimeline(timeline: Tweet[]) {
        await this.runtime.cacheManager.set(
            `twitter/${this.profile.username}/timeline`,
            timeline,
            { expires: Date.now() + 10 * 1000 }
        );

        const ownPosts = timeline.filter(
            (tweet) => tweet.userId === this.profile.id
        );
        if (ownPosts.length > 0) {
            await this.cacheOwnPosts(ownPosts);
        }
    }

    async cacheMentions(mentions: Tweet[]) {
        await this.runtime.cacheManager.set(
            `twitter/${this.profile.username}/mentions`,
            mentions,
            { expires: Date.now() + 10 * 1000 }
        );
    }

    async getCachedOwnPosts(count?: number): Promise<Tweet[]> {
        const cachedOwnPosts = await this.runtime.cacheManager.get<Tweet[]>(
            `twitter/${this.profile.username}/own_posts`
        );

        if (!cachedOwnPosts) {
            // Fallback: filter from cached timeline
            const cachedTimeline = await this.getCachedTimeline();
            if (cachedTimeline) {
                const ownPosts = cachedTimeline.filter(
                    (tweet) => tweet.userId === this.profile.id
                );
                return count ? ownPosts.slice(0, count) : ownPosts;
            }
            return [];
        }

        return count ? cachedOwnPosts.slice(0, count) : cachedOwnPosts;
    }

    async cacheOwnPosts(ownPosts: Tweet[]) {
        await this.runtime.cacheManager.set(
            `twitter/${this.profile.username}/own_posts`,
            ownPosts,
            { expires: Date.now() + 10 * 1000 }
        );
    }

    async getCachedCookies(username: string) {
        return await this.runtime.cacheManager.get<any[]>(
            `twitter/${username}/cookies`
        );
    }

    async cacheCookies(username: string, cookies: any[]) {
        await this.runtime.cacheManager.set(
            `twitter/${username}/cookies`,
            cookies
        );
    }

    async getCachedProfile(username: string) {
        return await this.runtime.cacheManager.get<TwitterProfile>(
            `twitter/${username}/profile`
        );
    }

    async cacheProfile(profile: TwitterProfile) {
        await this.runtime.cacheManager.set(
            `twitter/${profile.username}/profile`,
            profile
        );
    }

    async fetchProfile(username: string): Promise<TwitterProfile> {
        const cached = await this.getCachedProfile(username);

        if (cached) return cached;

        try {
            const profile = await this.requestQueue.add(async () => {
                const profile = await this.twitterClient.getProfile(username);
                return {
                    id: profile.userId,
                    username,
                    screenName: profile.name || this.runtime.character.name,
                    bio: profile.biography
                        ? profile.biography
                        : typeof this.runtime.character.bio === "string"
                          ? (this.runtime.character.bio as string)
                          : this.runtime.character.bio.length > 0
                            ? this.runtime.character.bio[0]
                            : "",
                    nicknames:
                        this.runtime.character.twitterProfile?.nicknames || [],
                } satisfies TwitterProfile;
            });

            this.cacheProfile(profile);

            return profile;
        } catch (error) {
            console.error("Error fetching Twitter profile:", error);

            return undefined;
        }
    }

    async fetchProfileWithRetry(
        username: string,
        maxRetries: number = 3
    ): Promise<TwitterProfile | undefined> {
        let lastError: any;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                elizaLogger.info(
                    `Attempting to fetch profile for ${username} (attempt ${attempt}/${maxRetries})`
                );

                // Add timeout to profile fetching
                const profilePromise = this.fetchProfile(username);
                const timeoutPromise = new Promise<never>((_, reject) =>
                    setTimeout(
                        () => reject(new Error("Profile fetch timeout")),
                        10000
                    )
                );

                const profile = await Promise.race([
                    profilePromise,
                    timeoutPromise,
                ]);

                if (profile) {
                    elizaLogger.info(
                        `Successfully fetched profile for ${username}`
                    );
                    return profile;
                }

                elizaLogger.warn(
                    `Profile fetch returned undefined for ${username}, attempt ${attempt}`
                );
            } catch (error) {
                lastError = error;
                elizaLogger.warn(
                    `Profile fetch failed for ${username}, attempt ${attempt}:`,
                    error
                );

                if (attempt < maxRetries) {
                    const delay = Math.min(
                        1000 * Math.pow(2, attempt - 1),
                        5000
                    ); // Exponential backoff, max 5s
                    elizaLogger.info(`Waiting ${delay}ms before retry...`);
                    await new Promise((resolve) => setTimeout(resolve, delay));
                }
            }
        }

        elizaLogger.error(
            `Failed to fetch profile for ${username} after ${maxRetries} attempts:`,
            lastError
        );
        return undefined;
    }
}
